<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList"
			bgColor="url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/gjbg.png') no-repeat center top/100% 417rpx #fff "
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="房屋估价" mpWeiXinShow :autoBack="true" bgColor="transparent" :fixed="false"
					class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="appraise_container">

					<!-- 白色表单区域 -->
					<view class="form_section">
						<view class="form_body">
							<view class="form_title">填写房屋信息</view>
							<view class="form_item" @click="showAddress">
								<view class="item_label">
									<text class="required">*</text>
									<text>城市</text>
								</view>
								<view class="item_content">
									<text :class="{ 'inactive': !form.city }">{{ form.city || '请选择' }}</text>
									<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
								</view>
							</view>
							<view class="form_item">
								<view class="item_label">
									<text class="required">*</text>
									<text>小区</text>
								</view>
								<view class="item_content">
									<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入小区名称" border="none"
										fontSize="28rpx" color="#333" v-model="form.community"
										inputAlign="right"></u--input>
								</view>
							</view>
							<view class="form_item" @click="selectRoomType">
								<view class="item_label">
									<text class="required">*</text>
									<text>房型</text>
								</view>
								<view class="item_content">
									<text :class="{ 'inactive': !form.roomType }">{{ form.roomType || '请选择' }}</text>
									<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
								</view>
							</view>
							<view class="form_item">
								<view class="item_label">
									<text class="required">*</text>
									<text>房屋面积</text>
								</view>
								<view class="item_content">
									<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入面积" border="none"
										fontSize="28rpx" color="#333" v-model="form.area" inputAlign="right"></u--input>
									<view class="unit">m²</view>
								</view>
							</view>
							<view class="form_item upload">
								<view class="item_label">
									<text class="required">*</text>
									<text>楼层信息</text>
								</view>
								<view class="item_content" style="justify-content: space-between;">
									<view class="form_column">
										<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入" border="none"
											fontSize="28rpx" color="#333" v-model="form.currentFloor"></u--input>
										<view class="unit">层</view>
									</view>
									<view class="form_column">
										<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入" border="none"
											fontSize="28rpx" color="#333" v-model="form.totalFloor"></u--input>
										<view class="unit">总层数</view>
									</view>
								</view>
							</view>
							<!-- 户型朝向 -->
							<view class="form_item" @click="selectOrientation">
								<view class="item_label">
									<text class="required">*</text>
									<text>户型朝向</text>
								</view>
								<view class="item_content">
									<text :class="{ 'inactive': !form.orientation }">{{ form.orientation || '请选择'
									}}</text>
									<u-icon name="arrow-right" color="#333333" size="24rpx"></u-icon>
								</view>
							</view>
							<!-- 装修类型 -->
							<view class="form_item upload">
								<view class="item_label">
									<text class="required">*</text>
									<text>装修类型</text>
								</view>
								<view class="item_content">
									<view class="tag_group">
										<view class="tag_item" :class="{ 'active': form.decorationType === '豪华装' }"
											@click="selectTag('decorationType', '豪华装')">
											<text>豪华装</text>
										</view>
										<view class="tag_item" :class="{ 'active': form.decorationType === '精装' }"
											@click="selectTag('decorationType', '精装')">
											<text>精装</text>
										</view>
										<view class="tag_item" :class="{ 'active': form.decorationType === '简装' }"
											@click="selectTag('decorationType', '简装')">
											<text>简装</text>
										</view>
										<view class="tag_item" :class="{ 'active': form.decorationType === '毛坯' }"
											@click="selectTag('decorationType', '毛坯')">
											<text>毛坯</text>
										</view>
									</view>
								</view>
							</view>

							<!-- 建筑类型 -->
							<view class="form_item upload">
								<view class="item_label">
									<text class="required">*</text>
									<text>建筑类型</text>
								</view>
								<view class="item_content">
									<view class="tag_group">
										<view class="tag_item" :class="{ 'active': form.buildingType === '塔楼' }"
											@click="selectTag('buildingType', '塔楼')">
											<text>塔楼</text>
										</view>
										<view class="tag_item" :class="{ 'active': form.buildingType === '板楼' }"
											@click="selectTag('buildingType', '板楼')">
											<text>板楼</text>
										</view>
										<view class="tag_item" :class="{ 'active': form.buildingType === '板塔结合' }"
											@click="selectTag('buildingType', '板塔结合')">
											<text>板塔结合</text>
										</view>
									</view>
								</view>
							</view>

							<!-- 是否有电梯 -->
							<view class="form_item upload">
								<view class="item_label">
									<text class="required">*</text>
									<text>是否有电梯</text>
								</view>
								<view class="item_content">
									<view class="tag_group">
										<view class="tag_item" :class="{ 'active': form.hasElevator === '有' }"
											@click="selectTag('hasElevator', '有')">
											<text>有</text>
										</view>
										<view class="tag_item" :class="{ 'active': form.hasElevator === '无' }"
											@click="selectTag('hasElevator', '无')">
											<text>无</text>
										</view>
									</view>
								</view>
							</view>
						</view>


					</view>
				</view>
			</view>
			<view slot="bottom">
				<!-- 提交按钮 -->
				<view class="bottom">
					<view class="submit_button" @click="submitForm">
						查看评估结果
					</view>
				</view>

			</view>
		</z-paging>
		<addressChoose level="2" ref="addressChoose" @confirm="confirmAddress"></addressChoose>

		<!-- 房型选择器 -->
		<u-picker :show="roomTypeShow" :columns="roomTypeList" :defaultIndex="roomTypeIndex" @confirm="confirmRoomType"
			@cancel="roomTypeShow = false" title="选择房型">
		</u-picker>

		<!-- 朝向选择器 -->
		<u-picker :show="orientationShow" :columns="[orientationList]" :defaultIndex="[orientationIndex]"
			@confirm="confirmOrientation" @cancel="orientationShow = false" title="选择朝向">
		</u-picker>

	</view>
</template>

<script>
import addressChoose from "@/components/common/address_choose.vue";

export default {
	components: {
		addressChoose
	},
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			// 表单数据

			form: {
				province: "",
				city: "",
				roomType: "", // 房型
				orientation: "", // 朝向
				decorationType: '精装', // 装修类型
				hasElevator: '有', // 是否有电梯
				buildingType: '板楼', // 建筑类型
			},
			// 房型选择器数据
			roomTypeShow: false,
			roomTypeList: [
				['1室', '2室', '3室', '4室', '5室', '6室'],
				['1厅', '2厅', '3厅', '4厅'],
				['1卫', '2卫', '3卫', '4卫']
			],
			// 朝向选择器数据
			orientationShow: false,
			orientationList: [
				'东', '南', '西', '北', '东南', '东北', '西南', '西北', '南北'
			],
		};
	},
	computed: {},
	onLoad() {

	},
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};

			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		// 通用单选标签方法
		selectTag(field, value) {
			this.form[field] = value;
		},
		confirmAddress(val, lat, lng) {
			this.form.longitude = lng;
			this.form.latitude = lat;
			if (val[0]) this.form.province = val[0];
			if (val[1]) this.form.city = val[1];
			if (val[2]) this.form.area = val[2];
			if (val[3]) this.form.town = val[3];
		},
		showAddress() {
			this.$refs.addressChoose.init();
		},
		// 选择房型
		selectRoomType() {
			this.roomTypeShow = true;
		},
		// 确认房型选择
		confirmRoomType(e) {
			const { indexs, value } = e;
			this.roomTypeIndex = indexs;
			this.form.roomType = value.join('');
			this.roomTypeShow = false;
		},
		// 选择朝向
		selectOrientation() {
			this.orientationShow = true;
		},
		// 确认朝向选择
		confirmOrientation(e) {
			const { indexs, value } = e;
			this.orientationIndex = indexs[0];
			this.form.orientation = value[0];
			this.orientationShow = false;
		},
		// 选择装修类型
		selectDecoration(type) {
			this.decorationType = type;
		},
		// 选择电梯
		selectElevator(hasElevator) {
			this.hasElevator = hasElevator;
		},
		// 选择建筑类型
		selectBuilding(type) {
			this.buildingType = type;
		},
		// 提交表单
		submitForm() {
			console.log('提交表单');
			uni.showToast({
				title: '提交成功',
				icon: 'success'
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		margin-top: 290rpx;

		.form_body {
			width: 100%;
			background-color: #fff;

			// 表单标题
			.form_title {
				font-size: 36rpx;
				font-weight: bold;
				color: #000000;
				line-height: 36rpx;
			}

			.form_item {
				width: 100%;
				height: auto;
				@include flex-center(row, space-between, center);
				padding: 30rpx 0;
				border-bottom: 1rpx solid #eee;

				// 表单行
				.form_column {
					width: 33%;
					@include flex-center(row, space-between, center);
				}

				.item_label {
					@include flex-center(row, flex-start, center);
					font-size: 28rpx;
					font-weight: bold;
					color: #333333;

					.required {
						color: #F63030
					}

					.subtitle {
						font-weight: 500;
						font-size: 22rpx;
						color: #BFBFBF;
						margin-left: 15rpx;
					}
				}

				.item_content {
					width: 100%;
					flex: 1;
					@include flex-center(row, flex-end, center);
					gap: 20rpx;
					font-size: 28rpx;
					color: #333;

					.inactive {
						color: #BFBFBF;
					}

					.unit {
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
					}

					.tag_group {
						width: 100%;
						@include flex-center(row, flex-start, center);
						flex-wrap: wrap;
						gap: 20rpx;

						.tag_item {
							@include flex-center(row, center, center);
							width: 160rpx;
							height: 56rpx;
							border-radius: 60rpx;
							background-color: #F8F8F8;
							font-weight: 500;
							font-size: 26rpx;
							color: #333333;
							transition: all 0.3s;
							text-align: center;

							&.active {
								color: #006AFC;
								background-color: rgba(0, 106, 252, 0.1);
							}

							text {
								text-align: center;
								font-size: 26rpx;
							}
						}
					}

					/deep/.u-textarea {
						background: #F8F8F8;
						border-radius: 8rpx;
						padding: 20rpx;

						.u-textarea__field {
							font-weight: 500;
							font-size: 28rpx;
							color: #333;
						}
					}
				}

				&.upload {
					flex-direction: column;
					gap: 20rpx;

					>view {
						width: 100%;
					}

					.item_content {
						@include flex-center(row, flex-start, center);
						gap: 20rpx;

						image {
							height: 150rpx;
							width: 150rpx;
							border-radius: 10rpx;
						}

						.item_content_image {
							position: relative;
							height: 150rpx;
							width: 150rpx;
							border-radius: 10rpx;
							overflow: hidden;

							image {
								height: 100%;
								width: 100%;
							}

							.item_content_image_icon {
								position: absolute;
								top: 0;
								right: 0;
								padding: 5rpx;
								border-radius: 0 10rpx 0 10rpx;
								background-color: rgba(0, 0, 0, 0.5);
							}
						}
					}
				}
			}
		}

		.appraise_container {
			width: 100%;

			// 白色表单区域
			.form_section {
				background: #ffffff;
				border-radius: 30rpx 30rpx 0 0;
				margin-top: -30rpx;
				position: relative;
				z-index: 1;
				padding: 30rpx 25rpx 0 25rpx;
				box-shadow: 0px 6px 12px 0px rgba(216, 216, 216, 0.1608);




			}
		}


	}

	// 提交按钮
	.bottom {
		padding: 20rpx 25rpx;
		background-color: #fff;
		box-shadow: 0rpx -3rpx 6rpx 1rpx rgba(207, 207, 207, 0.27);
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

		.submit_button {
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			@include flex-center(row, center, center);
			color: #ffffff;
			font-size: 30rpx;
		}
	}

}
</style>