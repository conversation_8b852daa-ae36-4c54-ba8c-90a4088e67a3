<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="专业律所" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<!-- 轮播图 -->
				<view class="swiper_container">
					<u-swiper :list="swiperList" @change="e => currentSwiperIndex = e.current" :autoplay="false"
						height="350rpx" radius="16rpx">
						<view slot="indicator" class="indicator">
							<view class="indicator__dot" v-for="(item, index) in swiperList" :key="index"
								:class="[index === currentSwiperIndex && 'indicator__dot--active']">
							</view>
						</view>
					</u-swiper>


				</view>

				<!-- 推荐律师 -->
				<view class="lawyer_section">
					<view class="section_header">
						<view class="section_title">推荐律师</view>
						<view class="section_more" @click="handleMoreLawyers">
							<text>换一换</text>
							<u-icon name="reload" size="24rpx" color="#999999"></u-icon>
						</view>
					</view>
					<view class="lawyer_cards">
						<view class="lawyer_card" v-for="(lawyer, index) in lawyerList" :key="index"
							@click="handleLawyerClick(lawyer)">
							<view class="lawyer_avatar">
								<image :src="$t.getImgUrl()"></image>
							</view>
							<view class="lawyer_info">
								<view class="lawyer_name_row">
									<view class="lawyer_name">{{ lawyer.name }}</view>
									<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/zy.png"
										mode="scaleToFill" />
								</view>
								<view class="lawyer_desc">{{ lawyer.description }}</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 服务套餐 -->
				<view class="service_section">
					<view class="section_header">
						<view class="section_title">服务套餐</view>
						<view class="section_more" @click="handleMoreServices">
							<text>查看更多</text>
							<u-icon name="arrow-right" size="24rpx" color="#999999" top="1rpx"></u-icon>
						</view>
					</view>
					<view class="service_packages">
						<service-package v-for="(service, index) in serviceList" :key="index" :info="service"
							@click="handleServiceClick">
						</service-package>
					</view>
				</view>
			</view>
			<view slot="bottom">
			</view>
		</z-paging>
	</view>
</template>

<script>
import ServicePackage from '../components/service_package.vue';

export default {
	components: {
		ServicePackage
	},
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			currentSwiperIndex: 0,
			swiperList: [
				'https://img2.baidu.com/it/u=3970917306,880187556&fm=253&fmt=auto&app=120&f=JPEG?w=655&h=808',
				'https://img2.baidu.com/it/u=363858033,1221485415&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1067'
			],
			lawyerList: [
				{
					id: '1',
					name: '陈姗姗律师',
					avatar: 'https://image-resource.mastergo.com/97849028259449/97849028259451/89f48ae71d0c2ef0aa37162bad519828.png',
					description: '2010年取得法学学士学位\n有多年在北京大学教学经验'
				},
				{
					id: '2',
					name: '佘叁律师',
					avatar: 'https://image-resource.mastergo.com/97849028259449/97849028259451/3bbcc03656ad99b373a1f9cc8db32ddf.png',
					description: '湖南省法学会卫生法研究会\n第一届理事会'
				}
			],
			serviceList: [
				{
					id: '1',
					title: '合同审核套餐',
					services: ['内容审查', '修改合同', '法律咨询'],
					price: '1880',
					sales: '144'
				},
				{
					id: '2',
					title: '律师陪购全程套餐',
					services: ['办理贷款', '交房验收', '全程协助'],
					price: '3680',
					sales: '144'
				},
				{
					id: '3',
					title: '纠纷处理套餐',
					services: ['诉讼服务', '代理服务', '收集证据'],
					price: '8880',
					sales: '144'
				}
			]
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		swiperChange(e) {
			console.log(e);

			this.currentSwiperIndex = e.current;
		},
		swiperClick(index) {
			console.log('轮播图点击', index);
		},
		handleMoreLawyers() {
			console.log('换一换律师');
		},
		handleLawyerClick(lawyer) {
			console.log('律师点击', lawyer);
		},
		handleMoreServices() {
			console.log('查看更多服务');
		},
		handleServiceClick(service) {
			console.log('服务点击', service);
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		padding: 20rpx 25rpx;

		// 轮播图容器
		.swiper_container {
			width: 100%;
			position: relative;

			.indicator {
				@include flex(row);
				justify-content: center;

				.indicator__dot {
					height: 6px;
					width: 6px;
					border-radius: 100px;
					background-color: rgba(255, 255, 255, 0.35);
					margin: 0 5px;
					transition: background-color 0.3s;

					&--active {
						background-color: #ffffff;
					}
				}
			}

		}

		// 推荐律师区域
		.lawyer_section {
			margin-top: 20rpx;

			.section_header {
				@include flex-center(row, space-between, center);

				.section_title {
					font-size: 32rpx;
					font-weight: bold;
					color: #000000;
				}

				.section_more {
					@include flex-center(row, center, center);
					gap: 8rpx;
					font-size: 24rpx;
					color: #999999;
				}
			}

			.lawyer_cards {
				width: 100%;
				padding: 30rpx;
				background: #FFFFFF;
				border-radius: 14rpx;
				@include flex-center(row, space-between, flex-start);
				gap: 70rpx;
				margin-top: 20rpx;

				.lawyer_card {
					flex: 1;
					@include flex-center(column, center, center);
					gap: 15rpx;

					.lawyer_avatar {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
						overflow: hidden;

						>image {
							width: 100%;
							height: 100%;

						}
					}

					.lawyer_info {
						width: 100%;
						@include flex-center(column, center, center);
						gap: 10rpx;

						.lawyer_name_row {
							@include flex-center(row, center, center);

							.lawyer_name {
								font-size: 28rpx;
								font-weight: bold;
								color: #333333;
							}

							>image {
								width: 40rpx;
								height: 36rpx;
							}
						}

						.lawyer_desc {
							font-size: 22rpx;
							color: #929292;
							text-align: center;
							line-height: 30rpx;
							@include text_overflow(100%, 2);

						}
					}
				}
			}
		}

		// 服务套餐区域
		.service_section {
			margin-top: 20rpx;

			.section_header {
				@include flex-center(row, space-between, center);


				.section_title {
					font-size: 32rpx;
					font-weight: bold;
					color: #000000;
				}

				.section_more {
					@include flex-center(row, center, center);
					gap: 8rpx;
					font-size: 24rpx;
					color: #999999;
				}
			}

			.service_packages {
				margin-top: 20rpx;
				@include flex-center(column, flex-start, null);
			}
		}
	}
}
</style>