<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="实名认证" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="form_body">
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>姓名</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入您的姓名" border="none"
								fontSize="28rpx" color="#333" v-model="form.name" inputAlign="right"></u--input>
						</view>
					</view>
					<view class="form_item">
						<view class="item_label">
							<text class="required">*</text>
							<text>手机号</text>
						</view>
						<view class="item_content">
							<u--input placeholderStyle="color: #BFBFBF;" placeholder="请输入您的手机号码" border="none"
								fontSize="28rpx" color="#333" v-model="form.tel" inputAlign="right"></u--input>
						</view>
					</view>
				</view>
				<view class="form_body">
					<view class="form_item upload">
						<view class="item_label">
							<text class="required">*</text>
							<text>房产证</text>
						</view>
						<view class="item_content">
							<view class="item_content_image" v-for="(item, index) in form.picArr">
								<image :src="$t.getImgUrl(item)" mode="">
								</image>
								<view class="item_content_image_icon" @click="form.picArr.splice(index, 1)">
									<u-icon name="close" color="#fff" size="24rpx"></u-icon>
								</view>
							</view>
							<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/upload_btn.png" mode=""
								@click="chooseImage" v-if="form.picArr.length < 3">
							</image>
						</view>
					</view>
				</view>
				<view class="form_tips">
					<view class="form_tips_title">
						平台声明
					</view>
					<view class="form_tips_cont">
						实名认证目的是验证卖家身份、验证房产真实性及所有权归属，收
						集的信息仅用于平台内部审核。我们深知您的电话号码和不动产权
						证信息属于敏感个人信息，我们承诺将依据相关法律法规，采取严
						格的安全保护措施予以保护。
					</view>
				</view>
			</view>
			<view slot="bottom" class="bottom">
				<view class="calculate_button">
					确认提交
				</view>
			</view>
		</z-paging>
		<u-popup :show="show" mode="center" bgColor="transparent">
			<view class="pop">
				<view class="pop_cont">
					<view class="pop_cont_title">
						<image src="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/ptsm.png"
							mode="scaleToFill" />
					</view>
					<view class="pop_cont_main">
						实名认证目的是验证卖家身份、验证房产真实性及
						所有权归属，收集的信息仅用于平台内部审核。我
						们深知您的电话号码和不动产权证信息属于敏感个
						人信息，我们承诺将依据相关法律法规，采取严格
						的安全保护措施予以保护。
					</view>
					<view class="pop_cont_btn" @click="show = false">
						我已知晓
					</view>
				</view>
				<u-icon name="close-circle" color="#fff" size="70rpx" @click="show = false"></u-icon>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			form: {
				name: "",
				tel: "",
				picArr: []
			},
			show: true, // 弹窗显示状态
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		chooseImage() {
			if (this.form.picArr.length > 3) {
				this.$t.toast('最多上传3张图片');
				return
			};
			this.$t.chooseImgUpload().then((url) => {
				this.form.picArr.push(url);
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		padding: 20rpx 0;
		@include flex-center(column, null, null);
		gap: 20rpx;

		.form_body {
			width: 100%;
			padding: 0 25rpx;
			background-color: #fff;

			.form_item {
				width: 100%;
				height: auto;
				@include flex-center(row, space-between, center);
				padding: 30rpx 0;
				border-bottom: 1rpx solid #eee;

				.item_label {
					@include flex-center(row, flex-start, center);
					font-size: 28rpx;
					font-weight: bold;
					color: #333333;

					.required {
						color: #F63030
					}
				}

				.item_content {
					flex: 1;
					@include flex-center(row, flex-end, center);
					gap: 20rpx;

				}

				&.upload {
					flex-direction: column;
					gap: 20rpx;

					>view {
						width: 100%;
					}

					.item_content {
						@include flex-center(row, flex-start, center);
						gap: 20rpx;

						image {
							height: 150rpx;
							width: 150rpx;
							border-radius: 10rpx;
						}

						.item_content_image {
							position: relative;
							height: 150rpx;
							width: 150rpx;
							border-radius: 10rpx;
							overflow: hidden;

							image {
								height: 100%;
								width: 100%;
							}

							.item_content_image_icon {
								position: absolute;
								top: 0;
								right: 0;
								padding: 5rpx;
								border-radius: 0 10rpx 0 10rpx;
								background-color: rgba(0, 0, 0, 0.5);
							}
						}
					}
				}
			}
		}

		.form_tips {
			padding: 0 25rpx;

			.form_tips_title {
				font-weight: bold;
				font-size: 26rpx;
				color: #333333;
			}

			.form_tips_cont {
				font-weight: 500;
				font-size: 24rpx;
				color: #8B8B8B;
				line-height: 35rpx;
				margin-top: 10rpx;
			}
		}
	}

	.bottom {
		padding: 20rpx 25rpx;
		background-color: #ffffff;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

		.calculate_button {
			width: 100%;
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			@include flex-center(row, center, center);
			color: #ffffff;
			font-size: 30rpx;
		}
	}

	.pop {
		@include flex-center(column, center, center);
		gap: 20rpx;

		.pop_cont {
			width: 700rpx;
			padding: 30rpx;
			background: linear-gradient(180deg, #E3EEF8 0%, #FFFFFF 100%);
			border-radius: 20rpx;

			.pop_cont_title {
				@include flex-center(row, center, center);

				>image {
					width: 207rpx;
					height: 73rpx;
				}
			}

			.pop_cont_main {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #4A4A4A;
				line-height: 44rpx;
				margin-top: 20rpx;

			}

			.pop_cont_btn {
				@include flex-center(row, center, center);
				font-weight: 500;
				font-size: 30rpx;
				color: #FFFFFF;
				height: 88rpx;
				background: #006AFC;
				border-radius: 8rpx;
				margin-top: 40rpx;
			}
		}

	}
}
</style>