<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="确认下单" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar"> </cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<!-- 服务套餐信息 -->
				<view class="service_package_section">
					<view class="section_title">服务套餐</view>
					<view class="package_info_container">
						<view class="package_image">
							<image :src="$t.getImgUrl()" mode="aspectFill"></image>
						</view>
						<view class="package_details">
							<view>
								<view class="package_name">{{ serviceInfo.name }}</view>
								<view class="package_services">
									<view class="service_item">办理贷款</view>
									<view class="service_divider"></view>
									<view class="service_item">交房验收</view>
									<view class="service_divider"></view>
									<view class="service_item">全程协助</view>
								</view>
							</view>
							<view class="package_price_row">
								<view class="package_price">￥{{ serviceInfo.price }}</view>
								<view class="package_quantity">共1件</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 联系信息 -->
				<view class="contact_info_section">
					<view class="contact_item">
						<u-icon size="29rpx"
							name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_icon1.png"></u-icon>
						<view class="contact_content">
							<view class="contact_label">姓名</view>
							<view class="contact_input">
								<u--input placeholder="请填写您的姓名" border="none" fontSize="28rpx" color="#333"
									v-model="form.name" inputAlign="right"></u--input>
							</view>
						</view>
					</view>
					<view class="contact_divider"></view>
					<view class="contact_item">
						<u-icon size="29rpx"
							name="https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/order_icon2.png"></u-icon>

						<view class="contact_content">
							<view class="contact_label">联系电话</view>
							<view class="contact_input">
								<u--input placeholder="请填写您的电话" border="none" fontSize="28rpx" color="#333"
									v-model="form.phone" inputAlign="right"></u--input>
							</view>
						</view>
					</view>
				</view>

				<!-- 支付方式 -->
				<view class="payment_method_section">
					<view class="section_title">支付方式</view>
					<view class="payment_options">
						<view class="payment_option" @click="selectPaymentMethod('wechat')">
							<view class="payment_icon">
								<image
									src="https://image-resource.mastergo.com/97849028259449/97849028259451/b1f48fa03a12618bac9791dcfef3f9ec.png"
									mode="aspectFill"></image>
							</view>
							<view class="payment_label">微信支付</view>
							<view class="payment_radio">
								<u-icon v-if="selectedPayment === 'wechat'" name="checkmark-circle-fill" color="#006AFC"
									size="68"></u-icon>
								<u-icon v-else name="radio-button-off" color="#A4A4A4" size="68"></u-icon>
							</view>
						</view>
						<view class="payment_divider"></view>
						<view class="payment_option" @click="selectPaymentMethod('alipay')">
							<view class="payment_icon">
								<u-icon name="alipay-circle-fill" color="#009FE8" size="122"></u-icon>
							</view>
							<view class="payment_label">支付宝支付</view>
							<view class="payment_radio">
								<u-icon v-if="selectedPayment === 'alipay'" name="checkmark-circle-fill" color="#006AFC"
									size="68rpx"></u-icon>
								<u-icon v-else name="radio-button-off" color="#A4A4A4" size="68rpx"></u-icon>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom">
				<view class="bottom_payment_container">
					<view class="payment_total">
						<view class="total_price">￥{{ serviceInfo.price }}</view>
					</view>
					<view class="payment_button" @click="handlePayment">
						<view class="payment_button_text">确认支付</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			form: {
				name: '',
				phone: ''
			},
			selectedPayment: 'wechat', // 默认选择微信支付
			serviceInfo: {
				id: '',
				name: '律师陪购全程套餐',
				price: '1880.00'
			}
		};
	},
	computed: {},
	onLoad(options) {
		// 接收页面参数
		if (options.service_id) {
			this.serviceInfo = {
				id: options.service_id,
				name: options.service_name || '律师陪购全程套餐',
				price: options.price || '1880.00'
			};
		}
	},
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		selectPaymentMethod(method) {
			this.selectedPayment = method;
		},
		handlePayment() {
			// 验证表单
			if (!this.form.name.trim()) {
				uni.showToast({
					title: '请填写姓名',
					icon: 'none'
				});
				return;
			}
			if (!this.form.phone.trim()) {
				uni.showToast({
					title: '请填写联系电话',
					icon: 'none'
				});
				return;
			}

			// 处理支付逻辑
			console.log('支付信息:', {
				serviceId: this.serviceInfo.id,
				serviceName: this.serviceInfo.name,
				name: this.form.name,
				phone: this.form.phone,
				paymentMethod: this.selectedPayment,
				amount: parseFloat(this.serviceInfo.price)
			});

			uni.showToast({
				title: '支付功能待实现',
				icon: 'none'
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;
		padding: 20rpx 25rpx;
		@include flex_center(column, flex-start, null);
		gap: 20rpx;

		>view {
			width: 100%;
			padding: 20rpx 25rpx;
		}

		// 服务套餐信息
		.service_package_section {
			background: #FFFFFF;
			border-radius: 10rpx;
			padding: 32rpx 20rpx;

			.section_title {
				font-size: 32rpx;
				font-weight: bold;
				color: #000000;
				line-height: 32rpx;
				margin-bottom: 32rpx;
			}

			.package_info_container {
				@include flex-center(row, flex-start, flex-start);
				gap: 20rpx;

				.package_image {
					width: 200rpx;
					height: 200rpx;
					border-radius: 14rpx;
					overflow: hidden;

					image {
						width: 100%;
						height: 100%;
					}
				}

				.package_details {
					flex: 1;
					@include flex-center(column, space-between, flex-start);
					height: 200rpx;

					.package_name {
						font-size: 30rpx;
						font-weight: bold;
						color: #000000;
						line-height: 30rpx;
					}

					.package_services {
						@include flex-center(row, flex-start, center);
						gap: 8rpx;
						margin-top: 20rpx;

						.service_item {
							font-size: 24rpx;
							color: #999999;
							line-height: 24rpx;
						}

						.service_divider {
							width: 1rpx;
							height: 20rpx;
							background: #CDCDCD;
						}
					}

					.package_price_row {
						@include flex-center(row, space-between, flex-end);
						width: 100%;
						margin-top: 20rpx;

						.package_price {
							font-size: 34rpx;
							font-weight: bold;
							color: #EE1616;
							line-height: 34rpx;
						}

						.package_quantity {
							font-size: 26rpx;
							color: #A4A4A4;
							line-height: 26rpx;
						}
					}
				}
			}
		}

		// 联系信息
		.contact_info_section {
			background: #FFFFFF;
			border-radius: 14rpx;
			padding: 0 25rpx;

			.contact_item {
				@include flex-center(row, flex-start, center);
				gap: 12rpx;
				padding: 20rpx 0;


				.contact_content {
					flex: 1;
					@include flex-center(row, space-between, center);

					.contact_label {
						font-size: 30rpx;
						color: #333333;
						line-height: 30rpx;
					}

					.contact_input {
						flex: 1;
					}
				}
			}

			.contact_divider {
				height: 1rpx;
				background: rgba(112, 112, 112, 0.15);
				margin: 0 auto;
			}
		}

		// 支付方式
		.payment_method_section {
			background: #FFFFFF;
			border-radius: 10rpx;

			.section_title {
				font-size: 32rpx;
				font-weight: bold;
				color: #000000;
				line-height: 32rpx;
				margin-bottom: 32rpx;
			}

			.payment_options {
				@include flex-center(column, flex-start, stretch);

				.payment_option {
					@include flex-center(row, flex-start, center);
					gap: 20rpx;
					padding: 20rpx 0;

					.payment_icon {
						width: 61rpx;
						height: 60rpx;
						border-radius: 10rpx;
						border: 1rpx solid #F8F8F8;
						@include flex-center(row, center, center);
						overflow: hidden;

						image {
							width: 59rpx;
							height: 59rpx;
						}
					}

					.payment_label {
						flex: 1;
						font-size: 28rpx;
						color: #000000;
						line-height: 28rpx;
					}

					.payment_radio {
						width: 68rpx;
						height: 68rpx;
						@include flex-center(row, center, center);
					}
				}

				.payment_divider {
					width: 670rpx;
					height: 1rpx;
					background: #F1F1F1;
					margin: 0 auto;
				}
			}
		}
	}
}

// 底部支付按钮
.bottom_payment_container {
	height: 128rpx;
	background: #FFFFFF;
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.07);
	@include flex-center(row, space-between, center);
	padding: 20rpx;

	.payment_total {
		@include flex-center(row, center, center);

		.total_price {
			font-size: 36rpx;
			font-weight: bold;
			color: #EE1616;
			line-height: 36rpx;
		}
	}

	.payment_button {
		width: 370rpx;
		height: 88rpx;
		background: #006AFC;
		border-radius: 15rpx;
		@include flex-center(row, center, center);

		.payment_button_text {
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 32rpx;
		}
	}
}
</style>