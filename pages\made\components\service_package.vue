<template>
    <view class="service_package" @click="handleClick">
        <view class="package_image">
            <image :src="$t.getImgUrl()" mode="scaleToFill" />
        </view>
        <view class="package_info">
            <view>
                <view class="package_title">{{ info.title }}</view>
                <view class="package_services">
                    <view class="service_item" v-for="(service, index) in info.services" :key="index">
                        {{ service }}
                    </view>
                </view>
            </view>
            <view class="package_price_row">
                <view class="package_price">
                    <text class="price_symbol">￥</text>
                    <text class="price_number">{{ $t.getIntDec(info.price, 'int') }}</text>
                    <text class="price_decimal">{{ $t.getIntDec(info.price, 'dec') }}</text>
                </view>
                <view class="package_sales">{{ info.sales }}人付款</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'service-package',
    props: {
        info: {
            type: Object,
            default: () => ({
                id: '',
                title: '合同审核套餐',
                services: ['内容审查', '修改合同', '法律咨询'],
                price: '1880',
                sales: '144'
            })
        }
    },
    methods: {
        handleClick() {
            // 处理点击事件
            this.$emit('click', this.info);
        }
    }
}
</script>

<style lang="scss" scoped>
.service_package {
    width: 100%;
    @include flex-center(row, flex-start, flex-start);
    gap: 20rpx;
    padding: 20rpx;
    background: #FFFFFF;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .package_image {
        width: 220rpx;
        height: 220rpx;
        border-radius: 12rpx;

        >image {
            width: 100%;
            height: 100%;
            border-radius: 12rpx;
        }

    }

    .package_info {
        flex: 1;
        height: 220rpx;
        @include flex-center(column, space-between, flex-start);
        gap: 20rpx;

        .package_title {
            @include text_overflow(100%, 2);
            font-size: 30rpx;
            font-weight: bold;
            color: #333333;
            line-height: 30rpx;
        }

        .package_services {
            @include flex-center(row, flex-start, center, wrap);
            gap: 20rpx;
            margin-top: 15rpx;

            .service_item {
                font-size: 24rpx;
                color: #999999;
                position: relative;

                &:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    right: -10rpx;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 1rpx;
                    height: 20rpx;
                    background: #CDCDCD;
                }
            }
        }

        .package_price_row {
            width: 100%;
            @include flex-center(row, space-between, flex-end);

            .package_price {
                @include flex-center(row, flex-start, baseline);
                color: #EE1616;

                .price_symbol {
                    font-size: 20rpx;
                    font-weight: bold;
                }

                .price_number {
                    font-size: 34rpx;
                    font-weight: bold;
                }

                .price_decimal {
                    font-size: 20rpx;
                    font-weight: bold;
                }
            }

            .package_sales {
                font-size: 24rpx;
                color: #999999;
            }
        }
    }
}
</style>
